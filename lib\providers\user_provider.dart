import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/rating_model.dart';
import '../services/supabase_service.dart';

class UserProvider extends ChangeNotifier {
  UserModel? _selectedUser;
  List<RatingModel> _userRatings = [];
  RatingStats? _userRatingStats;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get selectedUser => _selectedUser;
  List<RatingModel> get userRatings => _userRatings;
  RatingStats? get userRatingStats => _userRatingStats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Load user profile
  Future<void> loadUserProfile(String userId) async {
    try {
      _setLoading(true);
      _clearError();

      _selectedUser = await SupabaseService.getUserProfile(userId);

    } catch (e) {
      _setError('فشل في تحميل بيانات المستخدم');
    } finally {
      _setLoading(false);
    }
  }

  // Load user ratings
  Future<void> loadUserRatings(String userId) async {
    try {
      _setLoading(true);
      _clearError();

      _userRatings = await SupabaseService.getUserRatings(userId);
      _calculateRatingStats();

    } catch (e) {
      _setError('فشل في تحميل التقييمات');
    } finally {
      _setLoading(false);
    }
  }

  // Calculate rating statistics
  void _calculateRatingStats() {
    if (_userRatings.isEmpty) {
      _userRatingStats = null;
      return;
    }

    final totalRatings = _userRatings.length;
    final averageRating = _userRatings
        .map((r) => r.rating)
        .reduce((a, b) => a + b) / totalRatings;

    final ratingDistribution = <int, int>{};
    int fiveStarCount = 0;
    int fourStarCount = 0;
    int threeStarCount = 0;
    int twoStarCount = 0;
    int oneStarCount = 0;

    for (final rating in _userRatings) {
      final star = rating.rating.round();
      ratingDistribution[star] = (ratingDistribution[star] ?? 0) + 1;
      
      switch (star) {
        case 5:
          fiveStarCount++;
          break;
        case 4:
          fourStarCount++;
          break;
        case 3:
          threeStarCount++;
          break;
        case 2:
          twoStarCount++;
          break;
        case 1:
          oneStarCount++;
          break;
      }
    }

    // Get top tags
    final tagCounts = <String, int>{};
    for (final rating in _userRatings) {
      for (final tag in rating.tags) {
        tagCounts[tag] = (tagCounts[tag] ?? 0) + 1;
      }
    }

    final topTags = tagCounts.entries
        .toList()
        ..sort((a, b) => b.value.compareTo(a.value))
        ..take(5);

    _userRatingStats = RatingStats(
      averageRating: averageRating,
      totalRatings: totalRatings,
      ratingDistribution: ratingDistribution,
      topTags: topTags.map((e) => e.key).toList(),
      fiveStarCount: fiveStarCount,
      fourStarCount: fourStarCount,
      threeStarCount: threeStarCount,
      twoStarCount: twoStarCount,
      oneStarCount: oneStarCount,
    );
  }

  // Update user profile
  Future<bool> updateUserProfile(UserModel user) async {
    try {
      _setLoading(true);
      _clearError();

      await SupabaseService.updateUserProfile(user);
      _selectedUser = user;
      
      return true;

    } catch (e) {
      _setError('فشل في تحديث البيانات');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Create rating
  Future<bool> createRating(RatingModel rating) async {
    try {
      _setLoading(true);
      _clearError();

      final ratingId = await SupabaseService.createRating(rating);
      
      // Add to user ratings if it's for the selected user
      if (_selectedUser?.id == rating.ratedUserId) {
        final newRating = rating.copyWith(id: ratingId);
        _userRatings.insert(0, newRating);
        _calculateRatingStats();
      }
      
      return true;

    } catch (e) {
      _setError('فشل في إضافة التقييم');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Get user badge text
  String getUserBadgeText(UserModel user) {
    if (user.badges.isEmpty) return '';
    
    final badgeMap = {
      'top_organizer': 'منظم متميز',
      'frequent_traveler': 'مسافر دائم',
      'verified_driver': 'سائق معتمد',
      'excellent_rating': 'تقييم ممتاز',
      'punctual': 'ملتزم بالمواعيد',
      'friendly': 'ودود',
      'helpful': 'متعاون',
      'clean_car': 'سيارة نظيفة',
      'safe_driver': 'سائق آمن',
      'experienced': 'خبير',
    };
    
    return user.badges
        .map((badge) => badgeMap[badge] ?? badge)
        .join(' • ');
  }

  // Get user experience level
  String getUserExperienceLevel(UserModel user) {
    if (user.totalTrips == 0) return 'جديد';
    if (user.totalTrips < 5) return 'مبتدئ';
    if (user.totalTrips < 20) return 'متوسط';
    if (user.totalTrips < 50) return 'متقدم';
    return 'خبير';
  }

  // Get user trust score (0-100)
  int getUserTrustScore(UserModel user) {
    int score = 0;
    
    // Base score for verified users
    if (user.isVerified) score += 20;
    
    // Rating score (0-40 points)
    if (user.rating > 0) {
      score += (user.rating * 8).round();
    }
    
    // Trip count score (0-30 points)
    if (user.totalTrips > 0) {
      score += (user.totalTrips * 2).clamp(0, 30).round();
    }
    
    // Profile completeness (0-10 points)
    int completeness = 0;
    if (user.profileImageUrl != null) completeness += 2;
    if (user.bio != null && user.bio!.isNotEmpty) completeness += 2;
    if (user.city != null) completeness += 2;
    if (user.dateOfBirth != null) completeness += 2;
    if (user.phone != null) completeness += 2;
    score += completeness;
    
    return score.clamp(0, 100);
  }

  // Get user activity status
  String getUserActivityStatus(UserModel user) {
    final now = DateTime.now();
    final daysSinceUpdate = now.difference(user.updatedAt).inDays;
    
    if (daysSinceUpdate <= 1) return 'نشط الآن';
    if (daysSinceUpdate <= 7) return 'نشط هذا الأسبوع';
    if (daysSinceUpdate <= 30) return 'نشط هذا الشهر';
    return 'غير نشط';
  }

  // Helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  void clearSelectedUser() {
    _selectedUser = null;
    _userRatings.clear();
    _userRatingStats = null;
    notifyListeners();
  }

  // Get rating summary text
  String getRatingSummaryText() {
    if (_userRatingStats == null || _userRatingStats!.totalRatings == 0) {
      return 'لا توجد تقييمات بعد';
    }
    
    final stats = _userRatingStats!;
    return '${stats.displayRating} (${stats.totalRatings} تقييم)';
  }

  // Get most common rating tags
  List<String> getTopRatingTags({int limit = 3}) {
    if (_userRatingStats == null) return [];
    return _userRatingStats!.topTags.take(limit).toList();
  }

  // Check if user can be rated
  bool canRateUser(String currentUserId, String targetUserId) {
    return currentUserId != targetUserId;
  }
}
