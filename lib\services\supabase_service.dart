import 'dart:io';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import '../models/message_model.dart';
import '../models/rating_model.dart';
import '../constants/app_constants.dart';

class SupabaseService {
  static final SupabaseClient _client = Supabase.instance.client;
  
  // Getters
  static SupabaseClient get client => _client;
  static User? get currentUser => _client.auth.currentUser;
  static String? get currentUserId => currentUser?.id;
  static bool get isAuthenticated => currentUser != null;

  // Auth Methods
  static Future<AuthResponse> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
    required String role,
    String? phone,
  }) async {
    final response = await _client.auth.signUp(
      email: email,
      password: password,
      data: {
        'full_name': fullName,
        'role': role,
        'phone': phone,
      },
    );
    
    if (response.user != null) {
      // Create user profile
      await _client.from('users').insert({
        'id': response.user!.id,
        'email': email,
        'full_name': fullName,
        'role': role,
        'phone': phone,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });
    }
    
    return response;
  }

  static Future<AuthResponse> signInWithEmail({
    required String email,
    required String password,
  }) async {
    return await _client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  static Future<void> signOut() async {
    await _client.auth.signOut();
  }

  static Future<void> resetPassword(String email) async {
    await _client.auth.resetPasswordForEmail(email);
  }

  // User Methods
  static Future<UserModel?> getCurrentUserProfile() async {
    if (!isAuthenticated) return null;
    
    final response = await _client
        .from('users')
        .select()
        .eq('id', currentUserId!)
        .single();
    
    return UserModel.fromJson(response);
  }

  static Future<UserModel?> getUserProfile(String userId) async {
    final response = await _client
        .from('users')
        .select()
        .eq('id', userId)
        .single();
    
    return UserModel.fromJson(response);
  }

  static Future<void> updateUserProfile(UserModel user) async {
    await _client
        .from('users')
        .update(user.toJson())
        .eq('id', user.id);
  }

  // Trip Methods
  static Future<List<TripModel>> getTrips({
    String? fromCity,
    String? toCity,
    DateTime? departureDate,
    String? tripType,
    int limit = 20,
    int offset = 0,
  }) async {
    var query = _client
        .from('trips')
        .select('''
          *,
          leader:users!trips_leader_id_fkey(*)
        ''')
        .eq('status', 'active')
        .order('departure_date', ascending: true)
        .range(offset, offset + limit - 1);

    if (fromCity != null) {
      query = query.eq('from_city', fromCity);
    }
    if (toCity != null) {
      query = query.eq('to_city', toCity);
    }
    if (departureDate != null) {
      query = query.gte('departure_date', departureDate.toIso8601String());
    }
    if (tripType != null && tripType != 'all') {
      query = query.eq('trip_type', tripType);
    }

    final response = await query;
    return (response as List).map((trip) => TripModel.fromJson(trip)).toList();
  }

  static Future<TripModel?> getTripById(String tripId) async {
    final response = await _client
        .from('trips')
        .select('''
          *,
          leader:users!trips_leader_id_fkey(*)
        ''')
        .eq('id', tripId)
        .single();
    
    return TripModel.fromJson(response);
  }

  static Future<String> createTrip(TripModel trip) async {
    final response = await _client
        .from('trips')
        .insert(trip.toJson())
        .select()
        .single();
    
    return response['id'] as String;
  }

  static Future<void> updateTrip(TripModel trip) async {
    await _client
        .from('trips')
        .update(trip.toJson())
        .eq('id', trip.id);
  }

  static Future<List<TripModel>> getUserTrips(String userId) async {
    final response = await _client
        .from('trips')
        .select('''
          *,
          leader:users!trips_leader_id_fkey(*)
        ''')
        .eq('leader_id', userId)
        .order('created_at', ascending: false);
    
    return (response as List).map((trip) => TripModel.fromJson(trip)).toList();
  }

  // Booking Methods
  static Future<String> createBooking(BookingModel booking) async {
    final response = await _client
        .from('bookings')
        .insert(booking.toJson())
        .select()
        .single();
    
    return response['id'] as String;
  }

  static Future<void> updateBooking(BookingModel booking) async {
    await _client
        .from('bookings')
        .update(booking.toJson())
        .eq('id', booking.id);
  }

  static Future<List<BookingModel>> getTripBookings(String tripId) async {
    final response = await _client
        .from('bookings')
        .select('''
          *,
          traveler:users!bookings_traveler_id_fkey(*),
          trip:trips!bookings_trip_id_fkey(*)
        ''')
        .eq('trip_id', tripId)
        .order('created_at', ascending: false);
    
    return (response as List).map((booking) => BookingModel.fromJson(booking)).toList();
  }

  static Future<List<BookingModel>> getUserBookings(String userId) async {
    final response = await _client
        .from('bookings')
        .select('''
          *,
          traveler:users!bookings_traveler_id_fkey(*),
          trip:trips!bookings_trip_id_fkey(*)
        ''')
        .eq('traveler_id', userId)
        .order('created_at', ascending: false);
    
    return (response as List).map((booking) => BookingModel.fromJson(booking)).toList();
  }

  // Message Methods
  static Future<String> sendMessage(MessageModel message) async {
    final response = await _client
        .from('messages')
        .insert(message.toJson())
        .select()
        .single();
    
    return response['id'] as String;
  }

  static Future<List<MessageModel>> getConversationMessages(String conversationId) async {
    final response = await _client
        .from('messages')
        .select('''
          *,
          sender:users!messages_sender_id_fkey(*),
          receiver:users!messages_receiver_id_fkey(*)
        ''')
        .eq('conversation_id', conversationId)
        .order('created_at', ascending: true);
    
    return (response as List).map((message) => MessageModel.fromJson(message)).toList();
  }

  static Future<List<ConversationModel>> getUserConversations(String userId) async {
    final response = await _client
        .from('conversations')
        .select('''
          *,
          leader:users!conversations_leader_id_fkey(*),
          traveler:users!conversations_traveler_id_fkey(*),
          last_message:messages(*)
        ''')
        .or('leader_id.eq.$userId,traveler_id.eq.$userId')
        .order('updated_at', ascending: false);
    
    return (response as List).map((conv) => ConversationModel.fromJson(conv)).toList();
  }

  // Rating Methods
  static Future<String> createRating(RatingModel rating) async {
    final response = await _client
        .from('ratings')
        .insert(rating.toJson())
        .select()
        .single();
    
    return response['id'] as String;
  }

  static Future<List<RatingModel>> getUserRatings(String userId) async {
    final response = await _client
        .from('ratings')
        .select('''
          *,
          rater:users!ratings_rater_id_fkey(*),
          rated_user:users!ratings_rated_user_id_fkey(*),
          trip:trips!ratings_trip_id_fkey(*)
        ''')
        .eq('rated_user_id', userId)
        .order('created_at', ascending: false);
    
    return (response as List).map((rating) => RatingModel.fromJson(rating)).toList();
  }

  // Storage Methods
  static Future<String> uploadImage({
    required File file,
    required String bucket,
    required String fileName,
  }) async {
    final response = await _client.storage
        .from(bucket)
        .upload(fileName, file);
    
    return _client.storage
        .from(bucket)
        .getPublicUrl(fileName);
  }

  static Future<void> deleteImage({
    required String bucket,
    required String fileName,
  }) async {
    await _client.storage
        .from(bucket)
        .remove([fileName]);
  }

  // Real-time subscriptions
  static RealtimeChannel subscribeToMessages(String conversationId, Function(Map<String, dynamic>) onMessage) {
    return _client
        .channel('messages:$conversationId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'messages',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'conversation_id',
            value: conversationId,
          ),
          callback: onMessage,
        )
        .subscribe();
  }

  static RealtimeChannel subscribeToBookings(String tripId, Function(Map<String, dynamic>) onBooking) {
    return _client
        .channel('bookings:$tripId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'trip_id',
            value: tripId,
          ),
          callback: onBooking,
        )
        .subscribe();
  }
}
