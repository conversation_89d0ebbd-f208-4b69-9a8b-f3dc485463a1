import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../constants/app_theme.dart';
import '../models/trip_model.dart';
import '../services/navigation_service.dart';
import '../generated/l10n.dart';
import 'rating_stars.dart';

class TripCard extends StatelessWidget {
  final TripModel trip;
  final VoidCallback? onTap;
  final bool showBookButton;
  final bool isCompact;

  const TripCard({
    super.key,
    required this.trip,
    this.onTap,
    this.showBookButton = true,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap ?? () => NavigationService.goToTripDetails(trip.id),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with route and price
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trip.route,
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          trip.title,
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.secondary,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      '${trip.price.toInt()} درهم',
                      style: theme.textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Trip image (if available)
              if (trip.imageUrls.isNotEmpty && !isCompact) ...[
                ClipRRect(
                  borderRadius: BorderRadius.circular(12),
                  child: CachedNetworkImage(
                    imageUrl: trip.imageUrls.first,
                    height: 120,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      height: 120,
                      color: AppColors.surfaceVariant,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      height: 120,
                      color: AppColors.surfaceVariant,
                      child: const Icon(
                        Icons.image_not_supported,
                        color: AppColors.textTertiary,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],
              
              // Trip details
              Row(
                children: [
                  Expanded(
                    child: _DetailItem(
                      icon: Icons.calendar_today,
                      label: '${trip.departureDate.day}/${trip.departureDate.month}',
                    ),
                  ),
                  Expanded(
                    child: _DetailItem(
                      icon: Icons.access_time,
                      label: trip.departureTime,
                    ),
                  ),
                  Expanded(
                    child: _DetailItem(
                      icon: Icons.airline_seat_recline_normal,
                      label: '${trip.availableSeats} متاح',
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Leader info and trip type
              Row(
                children: [
                  // Leader avatar
                  CircleAvatar(
                    radius: 16,
                    backgroundColor: AppColors.primary,
                    backgroundImage: trip.leader?.profileImageUrl != null
                        ? CachedNetworkImageProvider(trip.leader!.profileImageUrl!)
                        : null,
                    child: trip.leader?.profileImageUrl == null
                        ? Text(
                            trip.leader?.fullName.substring(0, 1) ?? 'ق',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  
                  const SizedBox(width: 8),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          trip.leader?.fullName ?? 'قائد الرحلة',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (trip.leader != null) ...[
                          Row(
                            children: [
                              RatingStars(
                                rating: trip.leader!.rating,
                                size: 12,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                trip.leader!.displayRating,
                                style: theme.textTheme.bodySmall?.copyWith(
                                  color: AppColors.textSecondary,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                  
                  // Trip type badge
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTripTypeColor(trip.tripType).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: _getTripTypeColor(trip.tripType),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      _getTripTypeText(trip.tripType),
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: _getTripTypeColor(trip.tripType),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              // Book button
              if (showBookButton && trip.hasAvailableSeats) ...[
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () => NavigationService.goToBookTrip(trip.id),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.secondary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(S.of(context).bookTrip),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getTripTypeColor(String tripType) {
    switch (tripType) {
      case 'women_only':
        return Colors.pink;
      case 'family_only':
        return Colors.orange;
      default:
        return AppColors.primary;
    }
  }

  String _getTripTypeText(String tripType) {
    switch (tripType) {
      case 'women_only':
        return 'نساء فقط';
      case 'family_only':
        return 'عائلات فقط';
      default:
        return 'مختلط';
    }
  }
}

class _DetailItem extends StatelessWidget {
  final IconData icon;
  final String label;

  const _DetailItem({
    required this.icon,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppColors.textSecondary,
        ),
        const SizedBox(width: 4),
        Flexible(
          child: Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textSecondary,
            ),
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }
}

class CompactTripCard extends StatelessWidget {
  final TripModel trip;
  final VoidCallback? onTap;

  const CompactTripCard({
    super.key,
    required this.trip,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return TripCard(
      trip: trip,
      onTap: onTap,
      showBookButton: false,
      isCompact: true,
    );
  }
}
