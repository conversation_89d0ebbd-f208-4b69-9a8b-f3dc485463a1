import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/auth_service.dart';
import '../services/supabase_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  bool get isTripLeader => _currentUser?.isTripLeader ?? false;
  bool get isTraveler => _currentUser?.isTraveler ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // Listen to auth state changes
    AuthService.authStateChanges.listen((AuthState state) {
      if (state.event == AuthChangeEvent.signedIn) {
        _loadUserProfile();
      } else if (state.event == AuthChangeEvent.signedOut) {
        _currentUser = null;
        notifyListeners();
      }
    });

    // Load current user if already authenticated
    if (AuthService.isAuthenticated) {
      _loadUserProfile();
    }
  }

  Future<void> _loadUserProfile() async {
    try {
      _setLoading(true);
      _currentUser = await AuthService.getCurrentUserProfile();
      _clearError();
    } catch (e) {
      _setError('فشل في تحميل بيانات المستخدم');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    required String role,
    String? phone,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.signUpWithEmail(
        email: email,
        password: password,
        fullName: fullName,
        role: role,
        phone: phone,
      );

      if (result.isSuccess) {
        // User profile will be loaded automatically via auth state listener
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ غير متوقع');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.signInWithEmail(
        email: email,
        password: password,
      );

      if (result.isSuccess) {
        // User profile will be loaded automatically via auth state listener
        return true;
      } else {
        _setError(result.message);
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ غير متوقع');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> signOut() async {
    try {
      _setLoading(true);
      await AuthService.signOut();
      _currentUser = null;
      _clearError();
    } catch (e) {
      _setError('فشل في تسجيل الخروج');
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> resetPassword(String email) async {
    try {
      _setLoading(true);
      _clearError();

      final result = await AuthService.resetPassword(email);
      
      if (!result.isSuccess) {
        _setError(result.message);
      }
      
      return result.isSuccess;
    } catch (e) {
      _setError('حدث خطأ غير متوقع');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<bool> updateProfile(UserModel updatedUser) async {
    try {
      _setLoading(true);
      _clearError();

      final success = await AuthService.updateUserProfile(updatedUser);
      
      if (success) {
        _currentUser = updatedUser;
        notifyListeners();
        return true;
      } else {
        _setError('فشل في تحديث البيانات');
        return false;
      }
    } catch (e) {
      _setError('حدث خطأ غير متوقع');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  Future<void> refreshUserProfile() async {
    await _loadUserProfile();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Validation helpers
  bool isValidEmail(String email) {
    return AuthService.isValidEmail(email);
  }

  bool isValidPassword(String password) {
    return AuthService.isValidPassword(password);
  }

  bool isValidMoroccanPhone(String phone) {
    return AuthService.isValidMoroccanPhone(phone);
  }

  String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!isValidEmail(email)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (!isValidPassword(password)) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? validateFullName(String? fullName) {
    if (fullName == null || fullName.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (fullName.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  String? validatePhone(String? phone) {
    if (phone != null && phone.isNotEmpty && !isValidMoroccanPhone(phone)) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    if (password != confirmPassword) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }
}
