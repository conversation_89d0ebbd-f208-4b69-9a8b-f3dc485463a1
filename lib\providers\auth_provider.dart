import 'package:flutter/foundation.dart';
// import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
// import '../services/auth_service.dart';
// import '../services/supabase_service.dart';

class AuthProvider extends ChangeNotifier {
  UserModel? _currentUser;
  bool _isLoading = false;
  String? _error;

  // Getters
  UserModel? get currentUser => _currentUser;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isAuthenticated => _currentUser != null;
  bool get isTripLeader => _currentUser?.isTripLeader ?? false;
  bool get isTraveler => _currentUser?.isTraveler ?? false;

  AuthProvider() {
    _initializeAuth();
  }

  void _initializeAuth() {
    // For now, create a mock user for testing
    _currentUser = UserModel(
      id: 'mock_user_1',
      email: '<EMAIL>',
      fullName: 'مستخدم تجريبي',
      role: 'traveler',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  // Simplified methods for testing
  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    required String role,
    String? phone,
  }) async {
    _setLoading(true);
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    _currentUser = UserModel(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      fullName: fullName,
      role: role,
      phone: phone,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _setLoading(false);
    return true;
  }

  Future<bool> signIn({
    required String email,
    required String password,
  }) async {
    _setLoading(true);
    await Future.delayed(const Duration(seconds: 1)); // Simulate network delay

    _currentUser = UserModel(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      email: email,
      fullName: 'مستخدم تجريبي',
      role: 'traveler',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _setLoading(false);
    return true;
  }

  Future<void> signOut() async {
    _setLoading(true);
    await Future.delayed(const Duration(milliseconds: 500));
    _currentUser = null;
    _setLoading(false);
  }

  Future<bool> resetPassword(String email) async {
    _setLoading(true);
    await Future.delayed(const Duration(seconds: 1));
    _setLoading(false);
    return true;
  }

  Future<bool> updateProfile(UserModel updatedUser) async {
    _setLoading(true);
    await Future.delayed(const Duration(milliseconds: 500));
    _currentUser = updatedUser;
    _setLoading(false);
    return true;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  // Validation helpers
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  bool isValidPassword(String password) {
    return password.length >= 6;
  }

  bool isValidMoroccanPhone(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    return RegExp(r'^(\+212|0)(6|7)[0-9]{8}$').hasMatch(cleanPhone);
  }

  String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب';
    }
    if (!isValidEmail(email)) {
      return 'البريد الإلكتروني غير صحيح';
    }
    return null;
  }

  String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة';
    }
    if (!isValidPassword(password)) {
      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
    }
    return null;
  }

  String? validateFullName(String? fullName) {
    if (fullName == null || fullName.isEmpty) {
      return 'الاسم الكامل مطلوب';
    }
    if (fullName.length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل';
    }
    return null;
  }

  String? validatePhone(String? phone) {
    if (phone != null && phone.isNotEmpty && !isValidMoroccanPhone(phone)) {
      return 'رقم الهاتف غير صحيح';
    }
    return null;
  }

  String? validateConfirmPassword(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب';
    }
    if (password != confirmPassword) {
      return 'كلمة المرور غير متطابقة';
    }
    return null;
  }
}
